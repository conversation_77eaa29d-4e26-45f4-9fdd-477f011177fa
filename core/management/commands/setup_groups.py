from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from core.models import User, Room, RoomMembership


class Command(BaseCommand):
    help = 'Setup user groups and permissions'

    def handle(self, *args, **options):
        # 创建基本用户组
        admin_group, created = Group.objects.get_or_create(name='Administrators')
        if created:
            self.stdout.write(self.style.SUCCESS('Created Administrators group'))
        
        moderator_group, created = Group.objects.get_or_create(name='Moderators')
        if created:
            self.stdout.write(self.style.SUCCESS('Created Moderators group'))
        
        premium_group, created = Group.objects.get_or_create(name='Premium Users')
        if created:
            self.stdout.write(self.style.SUCCESS('Created Premium Users group'))
        
        # 获取相关的权限
        room_content_type = ContentType.objects.get_for_model(Room)
        user_content_type = ContentType.objects.get_for_model(User)
        membership_content_type = ContentType.objects.get_for_model(RoomMembership)
        
        # 管理员权限 - 可以管理所有内容
        admin_permissions = Permission.objects.filter(
            content_type__in=[room_content_type, user_content_type, membership_content_type]
        )
        admin_group.permissions.set(admin_permissions)
        
        # 版主权限 - 可以管理房间和成员
        moderator_permissions = Permission.objects.filter(
            content_type__in=[room_content_type, membership_content_type],
            codename__in=['add_room', 'change_room', 'delete_room', 
                         'add_roommembership', 'change_roommembership', 'delete_roommembership']
        )
        moderator_group.permissions.set(moderator_permissions)
        
        # 高级用户权限 - 基本的房间创建权限
        premium_permissions = Permission.objects.filter(
            content_type=room_content_type,
            codename__in=['add_room', 'change_room']
        )
        premium_group.permissions.set(premium_permissions)
        
        self.stdout.write(self.style.SUCCESS('Successfully setup groups and permissions'))
        
        # 迁移现有用户的权限
        self.migrate_existing_users()
    
    def migrate_existing_users(self):
        """将现有用户的is_superuser和is_staff权限迁移到组系统"""
        admin_group = Group.objects.get(name='Administrators')
        moderator_group = Group.objects.get(name='Moderators')
        
        # 将超级用户添加到管理员组
        superusers = User.objects.filter(is_superuser=True)
        for user in superusers:
            user.groups.add(admin_group)
            self.stdout.write(f'Added {user.username} to Administrators group')
        
        # 将staff用户添加到版主组
        staff_users = User.objects.filter(is_staff=True, is_superuser=False)
        for user in staff_users:
            user.groups.add(moderator_group)
            self.stdout.write(f'Added {user.username} to Moderators group')
        
        self.stdout.write(self.style.SUCCESS('Successfully migrated existing user permissions'))
