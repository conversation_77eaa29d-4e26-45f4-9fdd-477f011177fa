import random
from typing import Any, Dict, Optional, <PERSON><PERSON>

from events.models import EventStep
from games.models import Game, PictionaryGame, PlayerScore
from games.words import WORD_LIST
from . import BaseEventHandler
from ..models import Room, User
from ..utils import database_sync_to_async


# ============================================================================
# 你画我猜数据库辅助函数 (这部分没有改动)
# ============================================================================

@database_sync_to_async
def get_pictionary_game(room_code):
    try:
        return PictionaryGame.objects.select_related('game__room', 'current_drawer').get(game__room__room_code=room_code, game__is_active=True)
    except PictionaryGame.DoesNotExist:
        return None

@database_sync_to_async
def end_pictionary_round(room_code):
    try:
        room = Room.objects.get(room_code=room_code)
        if room.status == Room.STATUS_IN_PROGRESS:
            # 【修复】不要设置为WAITING，保持IN_PROGRESS让后续逻辑决定
            # room.status = Room.STATUS_WAITING
            # room.save()
            if hasattr(room, 'game'):
                room.game.is_active = False
                room.game.save()
            return room.status
        return None
    except Room.DoesNotExist:
        return None

@database_sync_to_async
def update_scores(winner, drawer, room):
    winner_score, _ = PlayerScore.objects.get_or_create(room=room, player=winner)
    winner_score.score += 10; winner_score.save()
    drawer_score, _ = PlayerScore.objects.get_or_create(room=room, player=drawer)
    drawer_score.score += 5; drawer_score.save()
    scores = PlayerScore.objects.filter(room=room).order_by('-score')
    return {score.player.username: score.score for score in scores}


class PictionaryEventHandler(BaseEventHandler):
    """你画我猜环节处理器"""

    async def start_step(self, room: Room, step: EventStep) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        try:
            participants = list(room.participants.all())
            if len(participants) < 1:
                return None, "至少需要1名玩家才能开始游戏。"

            drawer = random.choice(participants)
            word = random.choice(WORD_LIST)

            room.status = Room.STATUS_IN_PROGRESS
            await database_sync_to_async(room.save)()

            game_session, _ = await database_sync_to_async(
                Game.objects.update_or_create
            )(room=room, defaults={'game_type': Game.GAME_PICTIONARY, 'is_active': True})

            await database_sync_to_async(
                PictionaryGame.objects.update_or_create
            )(game=game_session, defaults={'current_word': word, 'current_drawer': drawer})

            return {
                "drawer": drawer.username,
                "word": word,
                "duration": step.duration,
                "room_status": room.status,
                "step_info": {"step_type": step.step_type, "order": step.order}
            }, None
        except Exception as e:
            self.logger.error(f"Error starting pictionary game: {e}")
            return None, "启动游戏时发生错误"

    async def handle_action(self, action: str, user: User, payload: Dict[str, Any]) -> bool:
        """
        【新】统一处理你画我猜环节的所有动作。
        """
        if action == 'send_message':
            # 尝试将消息作为“猜词”来处理
            return await self._handle_guess_message(user, payload)
        
        if action == 'send_drawing':
            # 将数据作为“画笔轨迹”来处理
            return await self._handle_drawing_data(user, payload)
        
        # 如果是其他action，本处理器不关心，返回False
        return False

    async def _handle_guess_message(self, user: User, payload: Dict[str, Any]) -> bool:
        """处理猜词消息 (原handle_message逻辑)"""
        try:
            message = payload.get('message', '').strip()
            if not message:
                return False

            game = await get_pictionary_game(self.room_code)
            if not game or not game.current_word or not game.current_drawer:
                return False

            if user.id == game.current_drawer.id:
                # 绘画者不能猜词，但我们要消费掉这个事件，所以返回True
                return True

            if message.lower() == game.current_word.lower():
                await self._handle_correct_guess(user, game)
                # 猜对了，事件被完全处理，返回True
                return True

            # 没猜对，返回False，让这个消息能被当做普通聊天消息广播出去
            return False
        except Exception as e:
            self.logger.error(f"Error handling pictionary guess message: {e}")
            return False

    async def _handle_drawing_data(self, user: User, payload: Dict[str, Any]) -> bool:
        """处理绘图数据 (原handle_custom_action逻辑)"""
        try:
            path_data = payload.get('path_data')
            if not path_data:
                return True

            if not isinstance(path_data, dict) or not path_data.get('path') or not path_data.get('id'):
                self.logger.warning(f"Invalid path data received from user {user.username}")
                return True

            game = await get_pictionary_game(self.room_code)
            if game and user.id != game.current_drawer.id:
                await self.send_error_to_user("只有当前绘画者可以绘画。")
                return True

            path_str = path_data.get('path', '')
            if len(path_str) > 10000:
                self.logger.warning(f"Path too long from user {user.username}: {len(path_str)} characters")
                return True

            await self.broadcast_to_room('broadcast_drawing_data', {
                'path_data': {
                    'id': path_data['id'],
                    'path': path_str,
                    'color': path_data.get('color', 'black')
                }
            })
            # 绘图数据被成功处理，返回True
            return True
        except Exception as e:
            self.logger.error(f"Error handling drawing data: {e}")
            return True

    async def _handle_correct_guess(self, winner: User, game: PictionaryGame):
        """处理正确猜词 (无改动)"""
        try:
            updated_scores = await update_scores(
                winner=winner,
                drawer=game.current_drawer,
                room=game.game.room
            )
            new_room_status = await end_pictionary_round(self.room_code)
            if new_room_status:
                await self.broadcast_to_room('broadcast_round_over', {
                    'winner': winner.username,
                    'word': game.current_word,
                    'room_status': new_room_status,
                    'scores': updated_scores,
                })
        except Exception as e:
            self.logger.error(f"Error handling correct guess: {e}")

    async def handle_timeout(self) -> None:
        """处理你画我猜超时 (无改动)"""
        try:
            game = await get_pictionary_game(self.room_code)
            if game:
                new_room_status = await end_pictionary_round(self.room_code)
                if new_room_status:
                    await self.broadcast_to_room('broadcast_round_over', {
                        'winner': None,
                        'word': game.current_word,
                        'room_status': new_room_status,
                        'scores': {},
                        'timeout': True,
                    })
        except Exception as e:
            self.logger.error(f"Error handling pictionary timeout: {e}")

    async def handle_restart(self, user: User, payload: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """处理你画我猜重启 (无改动)"""
        try:
            room = await self.get_room_with_template()
            if not room:
                return None, '房间不存在。'
            current_step = await database_sync_to_async(
                lambda: room.event_template.steps.filter(order=room.current_step_order).first()
            )()
            if not current_step:
                return None, '无法找到当前环节信息。'
            return await self.start_step(room, current_step)
        except Exception as e:
            self.logger.error(f"Error restarting pictionary: {e}")
            return None, "重新开始游戏时发生错误。"
