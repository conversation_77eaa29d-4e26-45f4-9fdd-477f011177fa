# core/serializers.py

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from .models import User, Room, RoomMembership

class UserSerializer(serializers.ModelSerializer):
    groups = serializers.StringRelatedField(many=True, read_only=True)
    is_administrator = serializers.ReadOnlyField()
    is_moderator = serializers.ReadOnlyField()
    is_premium_user = serializers.ReadOnlyField()

    class Meta:
        model = User
        fields = ('id', 'username', 'password', 'currency', 'groups',
                 'is_administrator', 'is_moderator', 'is_premium_user')
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        user = User.objects.create_user(
            username=validated_data['username'],
            password=validated_data['password']
        )
        return user


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        # Add custom claims
        token['username'] = user.username
        return token

class RoomMembershipSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = RoomMembership
        fields = ['id', 'user', 'role', 'is_ready', 'joined_at']
        read_only_fields = ['joined_at']

class RoomSerializer(serializers.ModelSerializer):
    host = serializers.StringRelatedField()
    participants = serializers.StringRelatedField(many=True, read_only=True)
    memberships = RoomMembershipSerializer(source='roommembership_set', many=True, read_only=True)

    class Meta:
        model = Room
        # --- ADD 'status' TO THE FIELDS LIST ---
        fields = ['id', 'room_code', 'host', 'participants', 'created_at', 'status', 'memberships']
        read_only_fields = ['room_code', 'host', 'participants', 'memberships']
