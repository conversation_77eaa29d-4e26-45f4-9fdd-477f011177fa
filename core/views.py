import uuid
from datetime import date
from django.db import transaction
from django.shortcuts import get_object_or_404
from rest_framework import status, generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView

from events.models import EventTemplate
from .models import User, Room, CheckIn, RoomMembership
from .serializers import UserSerializer, RoomSerializer, CustomTokenObtainPairSerializer

class APIRootView(APIView):
    def get(self, request, *args, **kwargs):
        return Response({"message": "Welcome to the Tuanzi API!"})

class HealthCheckView(APIView):
    def get(self, request, *args, **kwargs):
        return Response({"status": "ok"}, status=status.HTTP_200_OK)

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = []

class JoinRoomView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, *args, **kwargs):
        room_code = request.data.get('room_code')
        if not room_code:
            return Response({"error": "Room code is required."}, status=status.HTTP_400_BAD_REQUEST)
        try:
            room = Room.objects.get(room_code__iexact=room_code)
        except Room.DoesNotExist:
            return Response({"error": "Room not found."}, status=status.HTTP_404_NOT_FOUND)
        # 加入房间时，默认角色为参与者
        membership, created = RoomMembership.objects.get_or_create(
            user=request.user,
            room=room,
            defaults={'role': RoomMembership.ROLE_PARTICIPANT}
        )
        serializer = RoomSerializer(room)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
class RoomCreateView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, *args, **kwargs):
        template_id = request.data.get('template_id')
        if not template_id: return Response({"error": "Template ID is required."}, status=status.HTTP_400_BAD_REQUEST)
        try:
            template = EventTemplate.objects.get(id=template_id)
        except EventTemplate.DoesNotExist:
            return Response({"error": "Template not found."}, status=status.HTTP_404_NOT_FOUND)
        while True:
            room_code = uuid.uuid4().hex.upper()[:6]
            if not Room.objects.filter(room_code=room_code).exists():
                break
        host = request.user
        room = Room.objects.create(host=host, event_template=template, room_code=room_code)
        # 创建房间时，房主角色为HOST
        RoomMembership.objects.create(user=host, room=room, role=RoomMembership.ROLE_HOST)
        serializer = RoomSerializer(room)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

# --- 新增的签到和用户状态API ---
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_status(request):
    user = request.user
    serializer = UserSerializer(user)
    return Response({"logged_in": True, "user": serializer.data})

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_check_in_status(request):
    today = date.today()
    checked_in = CheckIn.objects.filter(user=request.user, check_in_date=today).exists()
    return Response({'checked_in_today': checked_in})

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def perform_check_in(request):
    today = date.today()
    user = request.user
    if CheckIn.objects.filter(user=user, check_in_date=today).exists():
        return Response({"error": "你今天已经签到过了！"}, status=status.HTTP_409_CONFLICT)
    reward = 10 
    CheckIn.objects.create(user=user)
    user.currency += reward
    user.save(update_fields=['currency'])
    return Response({"success": True, "message": f"签到成功！获得 {reward} 虚拟货币！", "new_currency": user.currency}, status=status.HTTP_201_CREATED)